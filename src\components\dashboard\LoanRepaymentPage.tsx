import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import Notification from '../common/Notification';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  saveLoanRepaymentIllustration,
  validateLoanRepaymentData,
  type LoanRepaymentIllustrationData
} from '../../services/loanRepaymentService';
import { formatCurrency } from '../../utils/currencyFormatter';

// Add the type for the table rows
interface LoanRepaymentTableRow {
  age: number;
  policyYear: string;
  calendarYear: number;
  repaymentAmount: number;
}

const LoanRepaymentPage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, loadScenariosFromBackend } = useDashboard();

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);

  // New loan repayment strategy state
  const [modelLoanRepayment, setModelLoanRepayment] = useState(false);
  const [repaymentType, setRepaymentType] = useState('');

  // Option 1: Change to New Loan Repayment amount now
  const [newLoanAmount, setNewLoanAmount] = useState('');
  const [showNewLoanDropdown, setShowNewLoanDropdown] = useState(false);

  // Option 2: Modify the loan repayment age (following FaceAmount pattern)
  const [ageChangeNow, setAgeChangeNow] = useState(false);
  const [ageChangeLater, setAgeChangeLater] = useState(false);

  // Option 3: Lump Sum (One-time premium) now
  const [lumpSumAmount, setLumpSumAmount] = useState('');
  const [showLumpSumContainer, setShowLumpSumContainer] = useState(false);

  // Updated state for loan repayment by year data (similar to FaceAmount)
  const [loanRepaymentByYearData, setLoanRepaymentByYearData] = useState<{
    selectedTypes: { age: boolean; policyYear: boolean; calendarYear: boolean };
    ageRange: { start: number; end: number };
    policyYearRange: { start: number; end: number };
    calendarYearRange: { start: number; end: number };
    isEditing: boolean;
    tableData: LoanRepaymentTableRow[];
  }>({
    selectedTypes: { age: false, policyYear: false, calendarYear: false },
    ageRange: { start: 40, end: 100 },
    policyYearRange: { start: 1, end: 100 },
    calendarYearRange: { start: 2024, end: 2100 },
    isEditing: false,
    tableData: []
  });

  // Initialize ranges with actual current values when customer/policy data changes
  useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setLoanRepaymentByYearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Helper functions for current age/year
  const getCurrentYear = () => new Date().getFullYear();
  
  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dob = selectedCustomerData.details.DOB;

    // Handle different date formats
    let birthDate: Date;

    if (dob.includes('.')) {
      // Format: DD.MM.YYYY
      const dobParts = dob.split('.');
      if (dobParts.length !== 3) return 40;
      const [day, month, year] = dobParts.map(Number);
      // Safari-compatible date parsing - use YYYY/MM/DD format
      birthDate = new Date(`${year}/${month}/${day}`);
    } else if (dob.includes('/')) {
      // Format: MM/DD/YYYY or DD/MM/YYYY
      const dobParts = dob.split('/');
      if (dobParts.length !== 3) return 40;
      const [first, second, year] = dobParts.map(Number);
      // Assume MM/DD/YYYY format - Safari-compatible
      birthDate = new Date(`${year}/${first}/${second}`);
    } else if (dob.includes('-')) {
      // Format: YYYY-MM-DD - Convert to Safari-compatible format
      const safariSafeDob = dob.replace(/-/g, '/');
      birthDate = new Date(safariSafeDob);
    } else {
      return 40; // Default if format not recognized
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return Math.max(0, age); // Ensure age is not negative
  };

  // Calculate current policy year from issue date
  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      // Safari-compatible date parsing
      const safariSafeDate = issueDate.replace ? issueDate.replace(/-/g, '/') : issueDate;
      const issue = new Date(safariSafeDate);
      const today = new Date();
      
      // Check if date is valid before calculations
      if (isNaN(issue.getTime())) {
        return 1; // Default fallback if date is invalid
      }
      
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      // Calculate total months more accurately
      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1; // Add current month if we've passed the issue day
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1; // Default if no issue date
  };

  // Generate table data based on selected types and ranges (similar to FaceAmount)
  const generateLoanRepaymentTableData = (): LoanRepaymentTableRow[] => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = loanRepaymentByYearData;
    
    // Determine which range to use based on selected types
    let startYear = 0;
    let endYear = 0;
    
    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    } else {
      // No type selected, return empty array
      return [];
    }
    
    if (startYear === 0 || endYear === 0 || startYear > endYear) {
      // Return empty array if no valid range
      return [];
    }
    
    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const data: LoanRepaymentTableRow[] = [];
    
    // Limit to maximum 12 entries
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;
    
    for (let year = startYear; year <= actualEndYear; year++) {
      data.push({
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        repaymentAmount: 0 // Default value - user will enter manually
      });
    }
    return data;
  };

  // Update table data when selections change
  useEffect(() => {
    const newTableData = generateLoanRepaymentTableData();
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      tableData: newTableData
    }));
  }, [loanRepaymentByYearData.selectedTypes, loanRepaymentByYearData.ageRange, loanRepaymentByYearData.policyYearRange, loanRepaymentByYearData.calendarYearRange, selectedCustomerData, selectedPolicyData]);

  // Reset all scenario state
  const handleResetScenarios = () => {
    setModelLoanRepayment(false);
    setRepaymentType('');
    setNewLoanAmount('');
    setShowNewLoanDropdown(false);
    setAgeChangeNow(false);
    setAgeChangeLater(false);
    setLumpSumAmount('');
    setShowLumpSumContainer(false);
    // Reset table data
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      selectedTypes: { age: false, policyYear: false, calendarYear: false },
      isEditing: false,
      tableData: []
    }));
    setNotification({ message: 'All loan repayment scenarios have been reset!', type: 'success' });
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    if (repaymentType === option) {
      setRepaymentType(''); // Deselect if already selected
    } else {
      setRepaymentType(option);
    }

    // Reset all option-specific states first
    setShowNewLoanDropdown(false);
    setShowLumpSumContainer(false);

    // Show relevant UI based on selected option
    if (option === 'new-loan-amount') {
      setShowNewLoanDropdown(true);
    } else if (option === 'lump-sum') {
      setShowLumpSumContainer(true);
    } else if (option === 'modify-age') {
      // Reset the year data when this option is selected
      setLoanRepaymentByYearData(prev => ({
        ...prev,
        selectedTypes: { age: false, policyYear: false, calendarYear: false },
        isEditing: true, // Enable editing by default
        tableData: []
      }));
    }
  };

  // Save scenario function
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      setNotification({ message: 'Please select a customer and policy first!', type: 'error' });
      return;
    }

    // Validate that at least one option is selected
    if (!repaymentType) {
      setNotification({ message: 'Please select a loan repayment option before saving!', type: 'error' });
      return;
    }

    setIsSaving(true);
    try {
      // Get policy ID from selected policy data
      const policyId = parseInt(selectedPolicyData.id) || parseInt(selectedCustomerData.customerId);

      if (!policyId) {
        throw new Error('Policy ID not found in selected data');
      }

      // Prepare data for backend API
      const loanRepaymentData: LoanRepaymentIllustrationData = {
        policy_id: policyId,
        repayment_type: repaymentType,
        new_loan_amount: newLoanAmount ? parseFloat(newLoanAmount) : undefined,
        lump_sum_amount: lumpSumAmount ? parseFloat(lumpSumAmount) : undefined,
        schedule_data: repaymentType === 'modify-age' && loanRepaymentByYearData.tableData.length > 0
          ? loanRepaymentByYearData.tableData.map(row => ({
              age: row.age,
              policy_year: parseInt(row.policyYear.replace('Year ', '')),
              calendar_year: row.calendarYear,
              repayment_amount: row.repaymentAmount
            }))
          : undefined,
        age_range: loanRepaymentByYearData.selectedTypes.age
          ? loanRepaymentByYearData.ageRange
          : undefined,
        policy_year_range: loanRepaymentByYearData.selectedTypes.policyYear
          ? loanRepaymentByYearData.policyYearRange
          : undefined,
        calendar_year_range: loanRepaymentByYearData.selectedTypes.calendarYear
          ? loanRepaymentByYearData.calendarYearRange
          : undefined,
        selected_type: loanRepaymentByYearData.selectedTypes.age ? 'age'
          : loanRepaymentByYearData.selectedTypes.policyYear ? 'policyYear'
          : loanRepaymentByYearData.selectedTypes.calendarYear ? 'calendarYear'
          : undefined
      };

      // Validate data before sending
      const validationErrors = validateLoanRepaymentData(loanRepaymentData);
      if (validationErrors.length > 0) {
        setNotification({ message: `Validation errors: ${validationErrors.join(', ')}`, type: 'error' });
        return;
      }

      // Calculate current age and policy year
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();

      // Save to backend
      const result = await saveLoanRepaymentIllustration(loanRepaymentData, currentAge, currentPolicyYear);

      if (result.status === 'SUCCESS') {
        setNotification({ message: 'Loan Repayment illustration is saved successfully', type: 'success' });

        // Reload scenarios from database to get the real saved scenarios with database IDs
        try {
          if (selectedPolicyData?.id) {
            console.log('🔄 Reloading scenarios from database after Loan Repayment save...');
            await loadScenariosFromBackend(parseInt(selectedPolicyData.id));
            setNotification({ message: 'Loan Repayment illustration saved and loaded from database successfully!', type: 'success' });
            console.log('✅ Loan Repayment scenarios saved and reloaded from database');
          }
        } catch (error) {
          console.error('❌ Error reloading scenarios from database:', error);
          setNotification({ message: 'Scenarios saved but error loading from database', type: 'warning' });
        }
      } else {
        throw new Error(result.message || 'Failed to save to database');
      }
    } catch (error) {
      console.error('Error saving scenario:', error);
      setNotification({ message: `Error saving scenario: ${error instanceof Error ? error.message : 'Unknown error'}`, type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">No Policy Selected</h3>
              <p className="text-yellow-700">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Loan Repayment illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Scenario Description */}
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <div className="text-black">
              <h3 className="text-lg font-semibold mb-2">Loan Repayment Scenario Overview</h3>
              <p>
                This scenario shows how repaying outstanding policy loans—fully or partially—affects your policy's future values. It highlights changes in policy debt, cash value recovery, and potential benefits of restoring policy health using the Current Interest Rate.
              </p>
            </div>
          </Card>
          
          {/* New Loan Repayment Strategy */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">Loan Repayment Strategy (scheduled or lump sum)</h2>
            <div className="space-y-6 pl-6 mt-4">
              
              {/* Option 1: Change to New Loan Repayment amount now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="new-loan-amount"
                    checked={repaymentType === 'new-loan-amount'}
                    onClick={() => handleOptionSelect('new-loan-amount')}
                    readOnly
                    className="mr-2"
                  />
                  Change to New Loan Repayment amount now
                </label>
                {repaymentType === 'new-loan-amount' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        value={newLoanAmount}
                        onChange={e => setNewLoanAmount(e.target.value)}
                        placeholder="Enter amount"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 2: Modify the loan repayment Schedule (Enhanced) */}
              <div className="space-y-4">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="modify-age"
                    checked={repaymentType === 'modify-age'}
                    onClick={() => handleOptionSelect('modify-age')}
                    readOnly
                    className="mr-2"
                  />
                  Modify the loan repayment Schedule
                </label>
                
                {repaymentType === 'modify-age' && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection Checkboxes - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.age}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ 
                              ...prev, 
                              selectedTypes: { age: e.target.checked, policyYear: false, calendarYear: false } 
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.policyYear}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ 
                              ...prev, 
                              selectedTypes: { age: false, policyYear: e.target.checked, calendarYear: false } 
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.calendarYear}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ 
                              ...prev, 
                              selectedTypes: { age: false, policyYear: false, calendarYear: e.target.checked } 
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Controls */}
                      {loanRepaymentByYearData.selectedTypes.age && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.ageRange.start <= calculateCurrentAge()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.ageRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.ageRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Controls */}
                      {loanRepaymentByYearData.selectedTypes.policyYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.policyYearRange.start <= calculateCurrentPolicyYear()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.policyYearRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.policyYearRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                     {/* Calendar Year Range Controls */}
                      {loanRepaymentByYearData.selectedTypes.calendarYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.calendarYearRange.start <= getCurrentYear()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.calendarYearRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.calendarYearRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Data Table - only show when a type is selected */}
                    {(loanRepaymentByYearData.selectedTypes.age || 
                      loanRepaymentByYearData.selectedTypes.policyYear || 
                      loanRepaymentByYearData.selectedTypes.calendarYear) && (
                      <>
                        <div className="flex justify-between items-center mt-6 mb-4">
                          <button className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                            View Year by Year Details
                          </button>
                          <button
                            onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                          >
                            {loanRepaymentByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                          </button>
                        </div>

                        {/* Enhanced Data Table with Manual Entry */}
                        <div className="mt-4">
                          <div className="overflow-x-auto">
                            <table className="w-full border-collapse border border-gray-300">
                              <thead>
                                <tr className="bg-gray-100">
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Repayment Amount</th>
                                </tr>
                              </thead>
                              <tbody>
                                {loanRepaymentByYearData.tableData.length === 0 ? (
                                  <tr>
                                    <td
                                      colSpan={4}
                                      className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                    >
                                      Select a range option above to populate the table
                                    </td>
                                  </tr>
                                ) : (
                                  loanRepaymentByYearData.tableData.map((row, index) => (
                                    <tr key={index} className="hover:bg-gray-50">
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.age}</td>
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.policyYear}</td>
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.calendarYear}</td>
                                      <td className="border border-gray-300 px-4 py-2">
                                        <input
                                          type="number"
                                          value={row.repaymentAmount}
                                          readOnly={!loanRepaymentByYearData.isEditing}
                                          onChange={(e) => {
                                            if (loanRepaymentByYearData.isEditing) {
                                              const newTableData = [...loanRepaymentByYearData.tableData];
                                              newTableData[index].repaymentAmount = parseFloat(e.target.value) || 0;
                                              setLoanRepaymentByYearData(prev => ({ ...prev, tableData: newTableData }));
                                            }
                                          }}
                                          className={`w-full p-2 border rounded text-black font-semibold ${
                                            loanRepaymentByYearData.isEditing
                                              ? 'border-blue-300 bg-white'
                                              : 'border-gray-300 bg-gray-100'
                                          }`}
                                          step="100"
                                        />
                                      </td>
                                    </tr>
                                  ))
                                )}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Option 3: Lump Sum (One-time premium) now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="lump-sum"
                    checked={repaymentType === 'lump-sum'}
                    onClick={() => handleOptionSelect('lump-sum')}
                    readOnly
                    className="mr-2"
                  />
                  Lump Sum (One-time premium) now
                </label>
                {repaymentType === 'lump-sum' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        value={lumpSumAmount}
                        onChange={e => setLumpSumAmount(e.target.value)}
                        placeholder="Enter lump sum amount"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 4: Interest only Payment */}
              <label className="flex items-center text-lg font-semibold text-black mb-2">
                <input
                  type="radio"
                  name="repaymentType"
                  value="interest-only"
                  checked={repaymentType === 'interest-only'}
                  onClick={() => handleOptionSelect('interest-only')}
                  readOnly
                  className="mr-2"
                />
                Interest only Payment
              </label>
            </div>
          </Card>
          
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
              disabled={isSaving}
            >
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Loan Repayment Illustration'}</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default LoanRepaymentPage;