import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FileText, Info, Trash2, BarChart3, CheckSquare, Square, MessageSquare, X, AlertTriangle } from 'lucide-react';
import { ResponsiveContainer, AreaChart, Area, CartesianGrid, XAxis, YAxis, Tooltip, Legend } from 'recharts';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import type { Scenario as BaseScenario } from '../../types';
import {
  initializeDisclosureData,
  type DisclosureItem
} from '../../services/disclosureService';
import {
  prepareChartData,
  calculateKeyMetrics,
  type ScenarioTableData
} from '../../services/scenarioService';
import {
  generateAsIsLoanTableData,
  generateFaceAmountTableData,
  generateFaceAmountVariesByYearTableData,
  generatePremiumAmountTableData,
  generateStopPremiumAmountTableData,
  AsIsLoan,
  FaceAmountVariesByYear,
  FaceAmount,
  StopPremiumAmount,
  PremiumAmount
} from '../../data/mockScenarioData';
import { formatCurrency, formatCurrencyForTable, formatPercentage } from '../../utils/currencyFormatter';

type ScenarioWithKeyPoints = BaseScenario & { keyPoints?: string[] };

// Enhanced date parsing functions (same as IllustrationMainPage)
const parseDate = (dateInput: any): Date | null => {
  if (!dateInput) {
    console.warn('parseDate: No date input provided');
    return null;
  }

  let dateStr = String(dateInput).trim();
  
  if (!dateStr) {
    console.warn('parseDate: Empty date string');
    return null;
  }

  console.log('parseDate: Processing date input:', dateStr);

  try {
    dateStr = dateStr.replace(/\s*(UTC|GMT|EST|PST|CST|MST).*$/i, '').trim();
    
    // Pattern 1: MM-DD-YYYY or MM/DD/YYYY
    const mmDdYyyyRegex = /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/;
    let match = dateStr.match(mmDdYyyyRegex);
    if (match) {
      const [, month, day, year] = match;
      const monthNum = parseInt(month, 10);
      const dayNum = parseInt(day, 10);
      const yearNum = parseInt(year, 10);
      
      if (isValidDateComponents(monthNum, dayNum, yearNum)) {
        const date = createSafeDate(yearNum, monthNum - 1, dayNum);
        if (date) {
          console.log('parseDate: Successfully parsed MM-DD-YYYY format:', date);
          return date;
        }
      }
    }

    // Pattern 2: DD-MM-YYYY or DD/MM/YYYY (European format)
    match = dateStr.match(mmDdYyyyRegex);
    if (match) {
      const [, first, second, year] = match;
      const firstNum = parseInt(first, 10);
      const secondNum = parseInt(second, 10);
      const yearNum = parseInt(year, 10);
      
      if (firstNum > 12 && secondNum <= 12 && isValidDateComponents(secondNum, firstNum, yearNum)) {
        const date = createSafeDate(yearNum, secondNum - 1, firstNum);
        if (date) {
          console.log('parseDate: Successfully parsed DD-MM-YYYY format:', date);
          return date;
        }
      }
    }

    // Pattern 3: YYYY-MM-DD or YYYY/MM/DD
    const yyyyMmDdRegex = /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/;
    match = dateStr.match(yyyyMmDdRegex);
    if (match) {
      const [, year, month, day] = match;
      const yearNum = parseInt(year, 10);
      const monthNum = parseInt(month, 10);
      const dayNum = parseInt(day, 10);
      
      if (isValidDateComponents(monthNum, dayNum, yearNum)) {
        const date = createSafeDate(yearNum, monthNum - 1, dayNum);
        if (date) {
          console.log('parseDate: Successfully parsed YYYY-MM-DD format:', date);
          return date;
        }
      }
    }

    // Pattern 4: ISO format (YYYY-MM-DDTHH:mm:ss)
    const isoRegex = /^(\d{4})-(\d{2})-(\d{2})T/;
    match = dateStr.match(isoRegex);
    if (match) {
      const [, year, month, day] = match;
      const yearNum = parseInt(year, 10);
      const monthNum = parseInt(month, 10);
      const dayNum = parseInt(day, 10);
      
      if (isValidDateComponents(monthNum, dayNum, yearNum)) {
        const date = createSafeDate(yearNum, monthNum - 1, dayNum);
        if (date) {
          console.log('parseDate: Successfully parsed ISO format:', date);
          return date;
        }
      }
    }

    // Pattern 5: Timestamp (numbers only)
    if (/^\d+$/.test(dateStr)) {
      const timestamp = parseInt(dateStr, 10);
      let date;
      
      if (dateStr.length >= 13) {
        date = new Date(timestamp);
      } else if (dateStr.length === 10) {
        date = new Date(timestamp * 1000);
      }
      
      if (date && isValidDate(date)) {
        console.log('parseDate: Successfully parsed timestamp:', date);
        return date;
      }
    }

    // Pattern 6: Try browser's native parsing as last resort
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      const nativeDate = new Date(dateStr + 'T00:00:00');
      if (isValidDate(nativeDate)) {
        console.log('parseDate: Successfully parsed with native parsing (ISO):', nativeDate);
        return nativeDate;
      }
    }

    console.error('parseDate: Could not parse date with any known pattern:', dateStr);
    return null;

  } catch (error) {
    console.error('parseDate: Exception during date parsing:', dateInput, error);
    return null;
  }
};

const isValidDateComponents = (month: number, day: number, year: number): boolean => {
  return (
    Number.isInteger(month) && month >= 1 && month <= 12 &&
    Number.isInteger(day) && day >= 1 && day <= 31 &&
    Number.isInteger(year) && year >= 1900 && year <= 2100
  );
};

const createSafeDate = (year: number, month: number, day: number): Date | null => {
  try {
    const date = new Date(year, month, day, 0, 0, 0, 0);
    
    if (date.getFullYear() === year && 
        date.getMonth() === month && 
        date.getDate() === day) {
      return date;
    }
    
    console.warn('createSafeDate: Date components were adjusted by JavaScript Date constructor');
    return null;
  } catch (error) {
    console.error('createSafeDate: Error creating date:', error);
    return null;
  }
};

const isValidDate = (date: Date): boolean => {
  return date instanceof Date && 
         !isNaN(date.getTime()) && 
         date.getFullYear() >= 1900 && 
         date.getFullYear() <= 2100;
};

const formatOrdinal = (n: number): string => {
  if (!Number.isInteger(n) || n <= 0) {
    return 'Unknown';
  }

  // Return just the number without ordinal suffix
  return n.toString();
};

// Function to get detailed table configuration and data based on scenario type
const getDetailedTableConfig = (scenario: any) => {
  console.log('📊 Getting detailed table config for scenario:', scenario);

  // Determine table type based on scenario name and category
  const scenarioName = scenario.name?.toLowerCase() || '';
  const scenarioCategory = scenario.category || '';

  // As-Is scenarios - use AsIsLoanTableData
  if (scenarioCategory === 'as-is' || scenarioName.includes('as-is')) {
    return {
      columns: AsIsLoan,
      data: generateAsIsLoanTableData(),
      title: 'As-Is Cash Value Analysis'
    };
  }

  // Face Amount change to new amount - use FaceAmountTableData
  if (scenarioCategory === 'face-amount' || scenarioName.includes('face amount')) {
    // Check if it's modify by year scenario - use FaceAmountVariesByYearTableData
    if (scenarioName.includes('modify') || scenarioName.includes('by year') || scenarioName.includes('varies')) {
      return {
        columns: FaceAmountVariesByYear,
        data: generateFaceAmountVariesByYearTableData(),
        title: 'Face Amount Varies By Year Analysis'
      };
    } else {
      return {
        columns: FaceAmount,
        data: generateFaceAmountTableData(),
        title: 'Face Amount Analysis'
      };
    }
  }

  // Premium scenarios
  if (scenarioCategory === 'premium' || scenarioName.includes('premium')) {
    // Check if it's stop premium scenario - use StopPremiumAmountTableData
    if (scenarioName.includes('stop') || scenarioName.includes('cease') || scenarioName.includes('future premium')) {
      return {
        columns: StopPremiumAmount,
        data: generateStopPremiumAmountTableData(),
        title: 'Stop Premium Analysis'
      };
    } else {
      // Premium enter new premium amount - use PremiumAmountTableData
      return {
        columns: PremiumAmount,
        data: generatePremiumAmountTableData(),
        title: 'Premium Amount Analysis'
      };
    }
  }

  // Default to As-Is table
  return {
    columns: AsIsLoan,
    data: generateAsIsLoanTableData(),
    title: 'Default Analysis'
  };
};

// Function to get hardcoded table data based on scenario
const getHardcodedTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Getting hardcoded table data for scenario:', scenario);

  // Map scenario categories to appropriate data functions
  const scenarioId = parseInt(scenario.id);

  let rawData: any[] = [];

  // Determine which hardcoded data to use based on scenario category or ID
  if (scenario.category === 'as-is' || scenarioId === 1) {
    rawData = generateAsIsLoanTableData();
  } else if (scenario.category === 'premium' || scenarioId === 2) {
    rawData = generatePremiumAmountTableData();
  } else if (scenario.category === 'face-amount' || scenarioId === 3) {
    rawData = generateFaceAmountTableData();
  } else if (scenario.category === 'policy-performance' || scenarioId === 4) {
    rawData = generateFaceAmountVariesByYearTableData();
  } else if (scenario.category === 'risk-assessment' || scenarioId === 5) {
    rawData = generateStopPremiumAmountTableData();
  } else {
    // Default to As-Is data
    rawData = generateAsIsLoanTableData();
  }

  // Convert the detailed hardcoded data to the simplified ScenarioTableData format
  const convertedData: ScenarioTableData[] = rawData.map((row, index) => ({
    policyYear: row.policyYear || (index + 1),
    endOfAge: row.age || (40 + index),
    plannedPremium: row.plannedPremium || 2500,
    netOutlay: (row.plannedPremium || 2500) * (index + 1), // Cumulative calculation
    netSurrenderValue: row.netCashValue || row.netValueBeginningOfYear || (1000 * (index + 1)),
    netDeathBenefit: row.faceAmount || 350000 // Use face amount or default
  }));

  console.log('✅ Converted hardcoded data:', {
    scenarioCategory: scenario.category,
    scenarioName: scenario.name,
    originalRows: rawData.length,
    convertedRows: convertedData.length,
    firstRow: convertedData[0],
    lastRow: convertedData[convertedData.length - 1],
    sampleData: convertedData.slice(0, 3) // Show first 3 rows for debugging
  });

  return convertedData;
};

const SelectedScenarios: React.FC = () => {
  const { scenarios, selectedCustomerData, selectedPolicyData, setActiveTab, deleteScenario, addScenario, loadScenariosFromBackend } = useDashboard();
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showComparisonSelected, setShowComparisonSelected] = useState(false);
  const [showAiCommentaryPopup, setShowAiCommentaryPopup] = useState(false);

  const [showDisclosurePopup, setShowDisclosurePopup] = useState(false);

  // Disclosure data state (only disclosure uses backend)
  const [disclosureData, setDisclosureData] = useState<DisclosureItem[]>([]);
  const [loadingDisclosure, setLoadingDisclosure] = useState(false);
  const [disclosureError, setDisclosureError] = useState<string | null>(null);

  // Table data state for selected scenario (using hardcoded data)
  const [scenarioTableData, setScenarioTableData] = useState<ScenarioTableData[]>([]);
  const [isLoadingChart, setIsLoadingChart] = useState(false);

  // Detailed table data state for full table display
  const [detailedTableData, setDetailedTableData] = useState<any[]>([]);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // Modern chart colors with gradients
  const chartColors = useMemo(() => ({
    plannedPremium: '#6366F1', // Modern indigo
    netOutlay: '#10B981',      // Emerald green
    netSurrenderValue: '#8B5CF6', // Purple
    netDeathBenefit: '#F59E0B'    // Amber
  }), []);



  // Calculate policy year with enhanced date parsing
  const calculatePolicyYear = React.useMemo((): string => {
    console.log('calculatePolicyYear: Starting calculation...');
    
    // Try to find issue date from multiple possible fields
    const possibleDateFields = [
      selectedPolicyData?.issueDate,
      selectedPolicyData?.policyStartDate
    ];

    console.log('calculatePolicyYear: Possible date fields:', possibleDateFields);

    let issueDate = null;
    let dateSource = '';

    // Find the first valid date
    for (let i = 0; i < possibleDateFields.length; i++) {
      if (possibleDateFields[i]) {
        issueDate = possibleDateFields[i];
        dateSource = `Field ${i} (${Object.keys(selectedPolicyData || {})[i] || 'customer data'})`;
        console.log(`calculatePolicyYear: Found date in ${dateSource}:`, issueDate);
        break;
      }
    }

    // If no issue date found, try to use a default or estimate
    if (!issueDate) {
      console.warn('calculatePolicyYear: No issue date found in any field');
      
      // Try to estimate based on customer age and policy type
      const dob = selectedCustomerData?.details?.DOB;
      if (dob) {
        console.log('calculatePolicyYear: Attempting to estimate based on DOB:', dob);
        const birthDate = parseDate(dob);
        if (birthDate) {
          // Assume policy was issued when customer was 25-35 years old (common age for life insurance)
          const estimatedIssueYear = birthDate.getFullYear() + 30; // Assume 30 years old
          const currentYear = new Date().getFullYear();
          const estimatedPolicyYear = currentYear - estimatedIssueYear + 1;
          
          if (estimatedPolicyYear > 0 && estimatedPolicyYear < 100) {
            console.log('calculatePolicyYear: Using estimated policy year:', estimatedPolicyYear);
            return `${formatOrdinal(estimatedPolicyYear)} (Estimated)`;
          }
        }
      }
      
      // Final fallback - assume it's a recent policy
      console.log('calculatePolicyYear: Using fallback - assuming 1st year policy');
      return '1st (Default)';
    }

    const parsedIssueDate = parseDate(issueDate);
    console.log('calculatePolicyYear: Parsed issue date:', parsedIssueDate, 'from', dateSource);
    
    if (!parsedIssueDate) {
      console.error('calculatePolicyYear: Failed to parse issue date:', issueDate);
      return 'Unknown (Invalid Date)';
    }

    // Get current date and normalize
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    console.log('calculatePolicyYear: Current date:', today);

    // Calculate year difference
    let yearDiff = today.getFullYear() - parsedIssueDate.getFullYear();
    console.log('calculatePolicyYear: Initial year difference:', yearDiff);
    
    // Create anniversary date for this year
    const anniversaryThisYear = new Date(parsedIssueDate);
    anniversaryThisYear.setFullYear(today.getFullYear());
    anniversaryThisYear.setHours(0, 0, 0, 0);
    console.log('calculatePolicyYear: Anniversary this year:', anniversaryThisYear);

    // If today is before this year's anniversary, we're still in the previous policy year
    if (today < anniversaryThisYear) {
      yearDiff--;
      console.log('calculatePolicyYear: Before anniversary, adjusted year diff:', yearDiff);
    }

    const currentPolicyYear = yearDiff + 1;
    console.log('calculatePolicyYear: Final policy year:', currentPolicyYear);

    // Validate the result
    if (!Number.isInteger(currentPolicyYear) || currentPolicyYear < 1) {
      console.error('calculatePolicyYear: Invalid policy year calculated:', currentPolicyYear);
      return 'Unknown (Invalid Calculation)';
    }

    if (currentPolicyYear > 100) {
      console.warn('calculatePolicyYear: Policy year seems too high:', currentPolicyYear);
      return 'Unknown (Year Too High)';
    }

    const result = `${formatOrdinal(currentPolicyYear)}`;
    console.log('calculatePolicyYear: Final result:', result);
    return result;
  }, [selectedPolicyData, selectedCustomerData]);

  // Show ONLY scenarios from database (not session scenarios)
  const displayScenarios: ScenarioWithKeyPoints[] = (scenarios as ScenarioWithKeyPoints[]).filter(scenario => {
    // Only show scenarios from database (numeric IDs, not session-* IDs)
    const isFromDatabase = !scenario.id.startsWith('session-') && !isNaN(Number(scenario.id));
    const matchesPolicy = selectedPolicyData?.id ? scenario.policyId === selectedPolicyData.id : true;
    const shouldShow = isFromDatabase && matchesPolicy;

    console.log('🔍 Filtering scenario:', {
      id: scenario.id,
      policyId: scenario.policyId,
      isFromDatabase,
      matchesPolicy,
      shouldShow
    });

    return shouldShow;
  });

  // Debug logging
  console.log('🔍 SelectedScenarios render - all scenarios:', scenarios.length, scenarios);
  console.log('🔍 SelectedScenarios render - database scenarios only:', displayScenarios.length, displayScenarios);
  console.log('🔍 SelectedScenarios render - selectedPolicyData:', selectedPolicyData?.id);

  // Log scenario sources for debugging
  const sessionScenarios = scenarios.filter(s => s.id.startsWith('session-'));
  const databaseScenarios = scenarios.filter(s => !s.id.startsWith('session-') && !isNaN(Number(s.id)));
  console.log('📊 Scenario breakdown:', {
    total: scenarios.length,
    session: sessionScenarios.length,
    database: databaseScenarios.length,
    displaying: displayScenarios.length
  });

  // Group scenarios by illustration type for summary
  const scenariosByType = displayScenarios.reduce((acc, scenario) => {
    const typeId = scenario.data?.backendData?.illustration_type_id || 'unknown';
    const typeName = scenario.data?.backendData?.illustration_type_description || 'Unknown Type';
    const key = `${typeId}-${typeName}`;

    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(scenario);
    return acc;
  }, {} as Record<string, any[]>);

  console.log('🔥 SCENARIOS BY ILLUSTRATION TYPE:');
  Object.entries(scenariosByType).forEach(([typeKey, scenarios]) => {
    console.log(`📊 ${typeKey}: ${scenarios.length} scenario(s)`);
    scenarios.forEach((scenario, index) => {
      console.log(`  📋 ${index + 1}. ID: ${scenario.id}, Question: ${scenario.data?.backendData?.illustration_question_description}`);
    });
  });

  // Log detailed scenario data for debugging
  if (displayScenarios.length > 0) {
    console.log('🔥 DATABASE SCENARIOS DETAILS:');
    displayScenarios.forEach((scenario, index) => {
      console.log(`📋 Scenario ${index + 1}:`, {
        id: scenario.id,
        name: scenario.name,
        policyId: scenario.policyId,
        typeId: scenario.data?.backendData?.illustration_type_id,
        typeName: scenario.data?.backendData?.illustration_type_description,
        question: scenario.data?.backendData?.illustration_question_description,
        option: scenario.data?.backendData?.illustration_option_description,
        faceAmount: scenario.data?.backendData?.new_face_amount,
        premiumAmount: scenario.data?.backendData?.new_premium_amount,
        startAge: scenario.data?.backendData?.illustration_starting_age,
        endAge: scenario.data?.backendData?.illustration_ending_age,
        retirementAge: scenario.data?.backendData?.retirement_age_goal,
        loanAmount: scenario.data?.backendData?.new_loan_amount,
        surrenderAmount: scenario.data?.backendData?.surrender_amount,
        backendDataExists: !!scenario.data?.backendData
      });
    });
  }

  const handleScenarioClick = (scenarioId: string) => {
    setSelectedScenario(selectedScenario === scenarioId ? null : scenarioId);
  };

  const handleDeleteScenario = async (scenarioId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click

    if (window.confirm('Are you sure you want to delete this scenario? This action cannot be undone.')) {
      try {
        await deleteScenario(scenarioId);
        // If the deleted scenario was currently selected, clear the selection
        if (selectedScenario === scenarioId) {
          setSelectedScenario(null);
        }
      } catch (error) {
        console.error('Failed to delete scenario:', error);
        alert('Failed to delete scenario. Please try again.');
      }
    }
  };

  // Fetch disclosure data and load scenarios from database when policy changes
  useEffect(() => {
    const fetchDataForPolicy = async () => {
      if (!selectedPolicyData?.id) return;

      // Initialize disclosure data
      await initializeDisclosureData(
        selectedCustomerData,
        selectedPolicyData,
        setLoadingDisclosure,
        setDisclosureError,
        setDisclosureData
      );

      // Load scenarios from database for selected policy
      try {
        const numericPolicyId = Number(selectedPolicyData.id);
        if (!Number.isNaN(numericPolicyId)) {
          console.log('� STARTING DATABASE FETCH - Policy ID:', numericPolicyId);
          console.log('🔥 API URL will be: http://127.0.0.1:8000/get_policy_scenarios?policy_id=' + numericPolicyId);

          // No test code needed - fresh start only

          // Load scenarios from database for this policy
          console.log('� Loading scenarios from database for policy:', numericPolicyId);
          await loadScenariosFromBackend(numericPolicyId);
          console.log('✅ Database scenarios loaded successfully');
        } else {
          console.warn('⚠️ Selected policy ID is not a number:', selectedPolicyData.id);
        }
      } catch (err) {
        console.error('❌ Failed to load policy scenarios:', err);
      }
    };

    fetchDataForPolicy();
  }, [selectedCustomerData?.customerId, selectedPolicyData?.id]);

  // Load table data when scenario is selected (using hardcoded data)
  useEffect(() => {
    const loadTableData = () => {
      if (!selectedScenario || !selectedPolicyData) {
        setScenarioTableData([]);
        setIsLoadingChart(false);
        return;
      }

      const scenario = displayScenarios.find(s => s.id === selectedScenario);
      if (!scenario) {
        console.warn('⚠️ Selected scenario not found in displayScenarios');
        setScenarioTableData([]);
        setIsLoadingChart(false);
        return;
      }

      console.log('🔄 Loading hardcoded table data for scenario:', {
        scenarioId: selectedScenario,
        policyId: selectedPolicyData.id,
        scenarioName: scenario.name,
        scenarioCategory: scenario.category
      });

      try {
        console.log('🔄 Starting data generation for scenario:', scenario);

        // Create guaranteed working data for chart demonstration
        console.log('📊 Creating guaranteed chart data...');

        // Always create working data regardless of scenario type
        const guaranteedTableData: ScenarioTableData[] = Array.from({ length: 25 }, (_, i) => ({
          policyYear: i + 1,
          endOfAge: 40 + i,
          plannedPremium: 2500,
          netOutlay: 2500 * (i + 1), // Cumulative
          netSurrenderValue: Math.max(0, (i + 1) * 1500 - 500), // Growing cash value
          netDeathBenefit: 350000 + (i * 1000) // Slightly growing death benefit
        }));

        // Try to get the proper data, but fall back to guaranteed data
        let tableData: ScenarioTableData[] = guaranteedTableData;
        try {
          const generatedData = getHardcodedTableData(scenario);
          if (generatedData && generatedData.length > 0) {
            tableData = generatedData;
            console.log('📊 Using generated table data:', tableData.length, 'rows');
          } else {
            console.log('📊 Generated data empty, using guaranteed data');
          }
        } catch (error) {
          console.error('❌ Error in getHardcodedTableData, using guaranteed data:', error);
        }

        // Get detailed table configuration and data
        let detailedConfig;
        try {
          detailedConfig = getDetailedTableConfig(scenario);
          console.log('📋 Generated detailed config:', detailedConfig);
        } catch (error) {
          console.error('❌ Error in getDetailedTableConfig:', error);
          detailedConfig = { data: [], columns: [], title: 'Chart Analysis' };
        }

        console.log('✅ Table data ready:', {
          rowCount: tableData.length,
          firstRow: tableData[0],
          lastRow: tableData[tableData.length - 1],
          detailedRowCount: detailedConfig.data.length,
          detailedColumns: detailedConfig.columns.length,
          tableTitle: detailedConfig.title
        });

        // Set the data (guaranteed to have content)
        setScenarioTableData(tableData);
        console.log('✅ Scenario table data set successfully with', tableData.length, 'rows');

        setDetailedTableData(detailedConfig.data);
        setTableColumns(detailedConfig.columns);

        // Clear loading state immediately for static data - force immediate render
        setIsLoadingChart(false);
        console.log('✅ All data set and loading cleared - chart should render immediately');

        // Force a small delay to ensure state updates are processed
        setTimeout(() => {
          console.log('📊 Final check - scenarioTableData length:', tableData.length);
        }, 50);
      } catch (error) {
        console.error('❌ Error in data loading process:', error);
        // Create guaranteed fallback data
        const emergencyFallbackData: ScenarioTableData[] = Array.from({ length: 20 }, (_, i) => ({
          policyYear: i + 1,
          endOfAge: 40 + i,
          plannedPremium: 2500,
          netOutlay: 2500 * (i + 1),
          netSurrenderValue: 1000 * (i + 1),
          netDeathBenefit: 350000
        }));
        setScenarioTableData(emergencyFallbackData);
        setDetailedTableData([]);
        setTableColumns([]);
        setIsLoadingChart(false);
        console.log('✅ Emergency fallback data set - chart should work now');
      }
    };

    loadTableData();
  }, [selectedScenario, displayScenarios, selectedPolicyData?.id]);

  // Memoized chart data preparation to prevent flickering
  const chartData = useMemo(() => {
    console.log('📊 Chart data preparation - scenarioTableData:', scenarioTableData);
    if (scenarioTableData.length === 0) {
      console.log('📊 Chart data empty - no scenario table data available');
      return [];
    }
    const data = prepareChartData(scenarioTableData);
    console.log('📊 Chart data prepared successfully:', {
      inputRows: scenarioTableData.length,
      outputRows: data.length,
      firstDataPoint: data[0],
      lastDataPoint: data[data.length - 1]
    });
    return data;
  }, [scenarioTableData]);

  // Custom tooltip formatter
  const formatTooltip = useCallback((value: any, name: string) => {
    return [formatCurrency(value), name];
  }, []);

  // Memoized area chart rendering function to prevent flickering
  const renderAreaChart = useCallback(() => {
    console.log('🎨 Rendering chart - isLoadingChart:', isLoadingChart, 'chartData length:', chartData?.length);
    console.log('🎨 Chart data sample:', chartData?.slice(0, 2));

    // Show loading only if explicitly loading and no data yet
    if (isLoadingChart && (!chartData || chartData.length === 0)) {
      console.log('🔄 Showing loading state');
      return (
        <div className="flex items-center justify-center h-[400px] bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-3"></div>
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        </div>
      );
    }

    if (!chartData || chartData.length === 0) {
      console.log('📊 No chart data available - chartData:', chartData);
      return (
        <div className="flex items-center justify-center h-[400px] bg-gray-50 rounded-lg">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">No chart data available</p>
            <p className="text-xs text-gray-400 mt-2">Scenario table data: {scenarioTableData.length} rows</p>
            <p className="text-xs text-gray-400">Chart data: {chartData?.length || 0} points</p>
          </div>
        </div>
      );
    }

    console.log('✅ Rendering actual chart with', chartData.length, 'data points');

    return (
      <ResponsiveContainer width="100%" height={400}>
        <AreaChart
          data={chartData}
          margin={{ top: 30, right: 40, left: 20, bottom: 20 }}
        >
          {/* Gradient Definitions */}
          <defs>
            <linearGradient id="plannedPremiumGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.plannedPremium} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.plannedPremium} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netOutlayGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netOutlay} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netOutlay} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netSurrenderValueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netSurrenderValue} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netSurrenderValue} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netDeathBenefitGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netDeathBenefit} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netDeathBenefit} stopOpacity={0.1}/>
            </linearGradient>
          </defs>

          {/* Modern Grid */}
          <CartesianGrid
            strokeDasharray="2 4"
            stroke="#e5e7eb"
            strokeOpacity={0.3}
            vertical={false}
          />

          {/* X-Axis */}
          <XAxis
            dataKey="year"
            axisLine={false}
            tickLine={false}
            tick={{
              fontSize: 12,
              fill: '#6b7280',
              fontWeight: 500
            }}
            tickMargin={10}
          />

          {/* Y-Axis */}
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{
              fontSize: 12,
              fill: '#6b7280',
              fontWeight: 500
            }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
            tickMargin={10}
          />

          {/* Modern Tooltip */}
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Policy Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.98)',
              border: 'none',
              borderRadius: '12px',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
              padding: '16px',
              fontSize: '14px',
              fontWeight: '500'
            }}
            labelStyle={{
              color: '#374151',
              fontWeight: '600',
              marginBottom: '8px'
            }}
          />

          {/* Modern Legend */}
          <Legend
            wrapperStyle={{
              paddingTop: '20px',
              fontSize: '14px',
              fontWeight: '500'
            }}
          />

          {/* Areas - Non-stacked for clearer visualization */}
          <Area
            type="monotone"
            dataKey="Net Death Benefit"
            stroke={chartColors.netDeathBenefit}
            fill="url(#netDeathBenefitGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netDeathBenefit,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Net Surrender Value"
            stroke={chartColors.netSurrenderValue}
            fill="url(#netSurrenderValueGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netSurrenderValue,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Net Outlay"
            stroke={chartColors.netOutlay}
            fill="url(#netOutlayGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netOutlay,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Planned Premium"
            stroke={chartColors.plannedPremium}
            fill="url(#plannedPremiumGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.plannedPremium,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  }, [chartData, chartColors, formatTooltip, isLoadingChart]);

  const selectedScenarioData = selectedScenario ? 
    displayScenarios.find(s => s.id === selectedScenario) : null;

  return (
    <div className="space-y-6 px-6">
      {/* Current Policy Information Card - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <Card className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Current Policy Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 font-medium">Policy Number</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.policyNumber || selectedPolicyData?.id || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Customer Name</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.name || selectedPolicyData?.name || 'John Michael Smith'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Customer ID</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.customerId || selectedPolicyData?.id || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Date of Birth</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.details?.DOB || '03-15-1985'}
                </p>
              </div>
            </div>
            
            {/* Second Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 font-medium">Policy Type</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedPolicyData?.name || 'Whole Life Insurance'}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 font-medium">Annual Premium</p>
                  <p className="text-base font-bold text-gray-900">
                    $ {selectedPolicyData?.premium ?
                      parseInt(selectedPolicyData.premium.replace(/[,$]/g, '').replace(/annually/i, '').trim().match(/(\d+)/)?.[1] || '0').toLocaleString() :
                      '2,500'}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Face Amount</p>
                <p className="text-base font-bold text-gray-900">
                  $ {selectedPolicyData?.coverage ?
                    (typeof selectedPolicyData.coverage === 'string' && typeof selectedPolicyData.faceAmount === 'number' ?
                      selectedPolicyData.faceAmount.toLocaleString() :
                      '350,000') :
                    '350,000'}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 font-medium">Current Policy Year</p>
                  <p className="text-base font-bold text-gray-900">
                    {calculatePolicyYear}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Action Buttons - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <div className="flex justify-end space-x-4">
          <Button
            variant="secondary"
            onClick={() => setShowDisclosurePopup(true)}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
          >
            <Info className="w-5 h-5" />
            <span>Disclosure</span>
          </Button>

          <Button
            variant="primary"
            onClick={() => setActiveTab('analysis-reports')}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            <FileText className="w-5 h-5" />
            <span>Get Illustration Reports</span>
          </Button>


        </div>
      )}

      {/* No Database Scenarios Message - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length === 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-lg font-bold">!</span>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-yellow-800">No Scenarios Found in Database</h3>
              <p className="text-yellow-700 mb-3">
                No saved scenarios found for this policy in the database. Create and save scenarios to see them here with interactive chart visualization.
              </p>
              <div className="flex space-x-3">
                <Button
                  variant="primary"
                  onClick={() => setActiveTab('illustrations')}
                >
                  Create Scenarios
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Policy Selection Required Message */}
      {!selectedPolicyData && (
        <Card className="bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-lg font-bold">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">Policy Selection Required</h3>
              <p className="text-yellow-700 mb-3">
                Please select a policy first to view your selected scenarios and illustrations.
              </p>
              <Button
                variant="primary"
                onClick={() => setActiveTab('policy-selection')}
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Grid - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayScenarios.map((scenario) => (
            <div
              key={scenario.id}
              onClick={() => handleScenarioClick(scenario.id)}
              className={`
                bg-white rounded-lg border p-5 cursor-pointer transition-all duration-200 relative
                ${selectedScenario === scenario.id
                  ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
                }
              `}
            >
              {/* Delete Button */}
              <button
                onClick={(e) => handleDeleteScenario(scenario.id, e)}
                className="absolute top-3 right-3 p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete scenario"
              >
                <Trash2 className="w-4 h-4" />
              </button>

              {/* Simple Title */}
              <div className="mb-4 pr-8">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900 text-lg">
                    {scenario.name || (() => {
                      switch(scenario.category) {
                        case 'as-is': return 'AS-IS Illustrations';
                        case 'face-amount': return 'Face Amount Illustrations';
                        case 'premium': return 'Premium Illustrations';
                        case 'income': return 'Income Illustrations';
                        case 'loan-repayment': return 'Loan Repayment Illustrations';
                        case 'interest-rate': return 'Interest Rate Illustrations';
                        case 'policy-lapse': return 'Policy Lapse Illustrations';
                        default: return 'Scenario Illustrations';
                      }
                    })()}
                  </h4>
                  {/* Selected Indicator */}
                  {selectedScenario === scenario.id && (
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                        SELECTED
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* IDs at top: Scenario ID, Policy ID, Illustration ID */}
              <div className="grid grid-cols-3 gap-2 mb-4 text-xs text-gray-600">
                <div><span className="font-medium">Scenario ID:</span> {scenario.data?.backendData?.scenario_id || scenario.id}</div>
                <div><span className="font-medium">Policy ID:</span> {scenario.data?.backendData?.policy_id || scenario.policyId}</div>
                <div><span className="font-medium">Illustration ID:</span> {scenario.data?.backendData?.illustration_id || 'N/A'}</div>
              </div>

              {/* Question - Exactly as shown in illustration form */}
              <div className="mb-3">
                <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Question</p>
                <p className="text-sm text-gray-900 font-medium">
                  {scenario.data?.backendData?.illustration_question_description || 'No question available'}
                </p>
              </div>

              {/* Option - Exactly as selected in illustration form */}
              <div className="mb-3">
                <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Option</p>
                <p className="text-sm text-gray-900">
                  {scenario.data?.backendData?.illustration_option_description || 'No option selected'}
                </p>
              </div>

              {/* Selected Option - Show exactly what you entered/selected */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Selected Option</p>
                <div className="space-y-1 text-sm text-gray-900">
                  {(() => {
                    const backendData = scenario.data?.backendData;
                    if (!backendData) return <div className="text-gray-500">No data available</div>;

                    const selectedValues = [];

                    // Show ONLY the specific option that was selected based on illustration type
                    const illustrationType = backendData.illustration_type_id;

                    switch (illustrationType) {
                      case 1: // AS-IS Type
                        selectedValues.push('Current policy analysis');
                        break;

                      case 2: // Face Amount Type
                        if (backendData.new_face_amount && backendData.new_face_amount > 0) {
                          selectedValues.push(`New Face Amount: $${backendData.new_face_amount.toLocaleString()}`);
                        }
                        break;

                      case 3: // Premium Type
                        if (backendData.new_premium_amount && backendData.new_premium_amount > 0) {
                          selectedValues.push(`New Premium: $${backendData.new_premium_amount.toLocaleString()}`);
                        }
                        break;

                      case 4: // Interest Rate Type
                        if (backendData.illustration_interest_rate && backendData.illustration_interest_rate > 0) {
                          selectedValues.push(`Interest Rate: ${(backendData.illustration_interest_rate * 100).toFixed(2)}%`);
                        }
                        break;

                      case 5: // Income/Surrender Type
                        if (backendData.surrender_amount && backendData.surrender_amount > 0) {
                          selectedValues.push(`Surrender Amount: $${backendData.surrender_amount.toLocaleString()}`);
                        }
                        if (backendData.retirement_age_goal && backendData.retirement_age_goal > 0) {
                          selectedValues.push(`Retirement Age: ${backendData.retirement_age_goal}`);
                        }
                        break;

                      case 6: // Loan Repayment Type
                        if (backendData.new_loan_amount && backendData.new_loan_amount > 0) {
                          selectedValues.push(`Loan Amount: $${backendData.new_loan_amount.toLocaleString()}`);
                        }
                        if (backendData.new_loan_repayment_amount && backendData.new_loan_repayment_amount > 0) {
                          selectedValues.push(`Repayment: $${backendData.new_loan_repayment_amount.toLocaleString()}`);
                        }
                        break;

                      default:
                        // Fallback - show the most relevant value
                        if (backendData.new_face_amount && backendData.new_face_amount > 0) {
                          selectedValues.push(`Face Amount: $${backendData.new_face_amount.toLocaleString()}`);
                        } else if (backendData.new_premium_amount && backendData.new_premium_amount > 0) {
                          selectedValues.push(`Premium: $${backendData.new_premium_amount.toLocaleString()}`);
                        }
                    }

                    // Always show age range if available
                    if (backendData.illustration_starting_age && backendData.illustration_ending_age) {
                      selectedValues.push(`Age Range: ${backendData.illustration_starting_age} to ${backendData.illustration_ending_age}`);
                    }

                    if (selectedValues.length === 0) {
                      return <div className="text-gray-500">No specific values selected</div>;
                    }

                    return (
                      <div className="space-y-1">
                        {selectedValues.map((value, index) => (
                          <div key={index} className="text-sm text-gray-900 font-medium">
                            {value}
                          </div>
                        ))}
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* View Button */}
              <div className="text-center pt-3 border-t border-gray-200">
                <span className="text-blue-600 text-sm font-medium">View Details</span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Inline Scenario Details and Table */}
      {selectedScenario && selectedScenarioData ? (
        (() => {
          try {
            return (
              <div className="mt-8 space-y-6">
                {/* NEW: Comparison Section - Above Key Performance Metrics */}
                <Card className="bg-white border border-gray-200">
                  <div className="flex items-center justify-between">
                    {/* Left side - Comparison checkbox */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => setShowComparisonSelected(!showComparisonSelected)}
                        className="flex items-center space-x-2 text-left"
                      >
                        {showComparisonSelected ? (
                          <CheckSquare className="w-5 h-5 text-blue-600" />
                        ) : (
                          <Square className="w-5 h-5 text-gray-400" />
                        )}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            Comparison of the Illustration
                          </h3>
                          <p className="text-sm text-gray-600">
                            {showComparisonSelected ? 'Comparison mode enabled' : 'Click to enable comparison mode'}
                          </p>
                        </div>
                      </button>
                    </div>

                    {/* Right side - AI Commentary button */}
                    <Button
                      variant="primary"
                      onClick={() => setShowAiCommentaryPopup(true)}
                      className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 font-medium"
                    >
                      <MessageSquare className="w-5 h-5" />
                      <span>AI Commentary</span>
                    </Button>
                  </div>
                </Card>

                {/* Key Performance Metrics */}
                <Card>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Performance Metrics</h3>
                  {(() => {
                    if (scenarioTableData.length === 0) {
                      return (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                          <div className="text-center">
                            <Info className="w-12 h-12 text-yellow-600 mx-auto mb-3" />
                            <h4 className="text-lg font-semibold text-yellow-800 mb-2">No Metrics Available</h4>
                            <p className="text-yellow-700 text-sm">
                              Click on a scenario card to view key performance metrics.
                            </p>
                          </div>
                        </div>
                      );
                    }

                    // Use the service method to calculate metrics from hardcoded data
                    const keyMetrics = calculateKeyMetrics(scenarioTableData);

                    return (
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <div className="text-2xl font-bold text-blue-600">
                            {formatCurrency(keyMetrics.totalPremiums)}
                          </div>
                          <div className="text-sm text-blue-700 font-medium">Total Premiums</div>
                        </div>
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600">
                            {formatCurrency(keyMetrics.totalOutlay)}
                          </div>
                          <div className="text-sm text-green-700 font-medium">Total Outlay</div>
                        </div>
                        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                          <div className="text-2xl font-bold text-purple-600">
                            {formatCurrency(keyMetrics.finalSurrenderValue)}
                          </div>
                          <div className="text-sm text-purple-700 font-medium">Final Cash Value</div>
                        </div>
                        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                          <div className="text-2xl font-bold text-orange-600">
                            {formatCurrency(keyMetrics.finalDeathBenefit)}
                          </div>
                          <div className="text-sm text-orange-700 font-medium">Final Death Benefit</div>
                        </div>
                        <div className="bg-teal-50 p-4 rounded-lg border border-teal-200">
                          <div className="text-2xl font-bold text-teal-600">
                            {Math.abs(keyMetrics.roi).toFixed(1)}%
                          </div>
                          <div className="text-sm text-teal-700 font-medium">ROI</div>
                        </div>
                      </div>
                    );
                  })()}
                </Card>

                {/* Modern Area Chart Section */}
                {scenarioTableData.length > 0 && (
                  <Card className="bg-white border border-gray-200 shadow-sm">
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mr-3">
                            <BarChart3 className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900">
                              Policy Value Visualization
                            </h3>
                            <p className="text-sm text-gray-500">
                              Interactive timeline analysis
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                            {chartData.length} Years
                          </div>
                        </div>
                      </div>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Track the evolution of your policy values over time. Each area represents a different financial component,
                        showing how premiums, cash value, and death benefits change throughout the policy lifecycle.
                      </p>
                    </div>
                    <div className="bg-white rounded-xl p-4 border border-gray-100">
                      {renderAreaChart()}
                    </div>
                  </Card>
                )}

                {/* Enhanced Detailed Illustration Table */}
                <Card>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex flex-col">
                      <h3 className="text-xl font-bold text-gray-900 flex items-center">
                        <BarChart3 className="w-6 h-6 text-blue-600 mr-2" />
                        {(() => {
                          const scenario = displayScenarios.find(s => s.id === selectedScenario);
                          if (scenario) {
                            const config = getDetailedTableConfig(scenario);
                            return config.title;
                          }
                          return 'Detailed Analysis';
                        })()}
                      </h3>
                      {/* Scenario Indicator */}
                      {(() => {
                        const scenario = displayScenarios.find(s => s.id === selectedScenario);
                        if (scenario) {
                          return (
                            <div className="flex items-center mt-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <span className="text-sm text-gray-600">
                                Displaying data for: <span className="font-medium text-gray-900">{scenario.name || 'Selected Scenario'}</span>
                              </span>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </div>

                  {/* Scrollable Table Container */}
                  <div className="bg-white rounded-lg border border-gray-200">
                    {detailedTableData.length > 0 && tableColumns.length > 0 ? (
                      <div className="overflow-auto" style={{ maxHeight: '600px' }}>
                        <table className="w-full min-w-max">
                          <thead className="bg-gray-50 sticky top-0 z-10">
                            <tr>
                              <th className="px-3 py-3 text-left font-bold text-gray-700 text-xs border-r border-gray-200 bg-gray-100">#</th>
                              {tableColumns.map((column, index) => (
                                <th
                                  key={index}
                                  className="px-3 py-3 text-left font-bold text-gray-700 text-xs border-r border-gray-200 whitespace-nowrap"
                                  style={{ minWidth: `${column.width || 120}px` }}
                                >
                                  {column.header}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200">
                            {detailedTableData.slice(0, 25).map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-gray-50 transition-colors">
                                <td className="px-3 py-2 text-gray-700 font-medium text-xs border-r border-gray-200 bg-gray-50 sticky left-0">
                                  {rowIndex + 1}
                                </td>
                                {tableColumns.map((column, colIndex) => (
                                  <td
                                    key={colIndex}
                                    className={`px-3 py-2 text-gray-700 text-xs border-r border-gray-200 whitespace-nowrap ${
                                      column.isCurrency ? 'table-cell-currency monospace-numbers' :
                                      column.key === 'interestRate' || column.key === 'loanInterestRate' ? 'table-cell-number monospace-numbers' :
                                      'table-cell-text'
                                    }`}
                                  >
                                    {column.isCurrency && row[column.key] !== null && row[column.key] !== undefined ? (
                                      <div className="currency-container">
                                        <span className="currency-symbol">$</span>
                                        <span className="currency-amount monospace-numbers">
                                          {formatCurrencyForTable(row[column.key]).amount}
                                        </span>
                                      </div>
                                    ) : column.key === 'interestRate' || column.key === 'loanInterestRate' ? (
                                      <span className="monospace-numbers">{formatPercentage(row[column.key])}</span>
                                    ) : row[column.key] !== null && row[column.key] !== undefined ? (
                                      row[column.key]
                                    ) : (
                                      '-'
                                    )}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="p-8 text-center">
                        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                        <p className="text-gray-500">No detailed table data available</p>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            );
          } catch (err) {
            return (
              <div className="mt-8 p-6 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <h3 className="text-lg font-semibold mb-2">Error displaying scenario details</h3>
                <p>There was a problem rendering the selected scenario. Please try another scenario or contact support if the problem persists.</p>
              </div>
            );
          }
        })()
      ) : selectedScenario ? (
        <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700">
          <h3 className="text-lg font-semibold mb-2">Scenario Not Found</h3>
          <p>The selected scenario could not be found. It may have been deleted or is invalid.</p>
        </div>
      ) : null}

      {/* AI Commentary Popup Modal */}
      {showAiCommentaryPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <MessageSquare className="w-5 h-5 text-purple-600 mr-2" />
                AI Commentary & Analysis
              </h2>
              <button
                onClick={() => setShowAiCommentaryPopup(false)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Modal Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-6">
                {/* Analysis Summary */}
                <div className="bg-purple-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <BarChart3 className="w-5 h-5 text-purple-600 mr-2" />
                    Policy Performance Analysis
                  </h3>
                  <div className="space-y-3 text-sm text-gray-700">
                    <p>
                      <strong>Overall Assessment:</strong> Based on the current scenario analysis, this policy demonstrates strong long-term value accumulation with consistent premium payments supporting both death benefit protection and cash value growth.
                    </p>
                    <p>
                      <strong>Key Strengths:</strong> The policy shows excellent liquidity potential through its cash value component, providing flexibility for future financial needs while maintaining substantial death benefit coverage.
                    </p>
                    <p>
                      <strong>Growth Trajectory:</strong> The cash value grows steadily over time, reaching significant amounts in later years, making this an effective tool for both protection and wealth accumulation.
                    </p>
                  </div>
                </div>

                {/* Recommendations */}
                <div className="bg-green-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <Info className="w-5 h-5 text-green-600 mr-2" />
                    AI Recommendations
                  </h3>
                  <div className="space-y-2 text-sm text-gray-700">
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Premium Strategy:</strong> Consider maintaining consistent premium payments to maximize cash value accumulation and policy performance.</p>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Review Schedule:</strong> Annual policy reviews are recommended to ensure the illustration remains aligned with your financial goals.</p>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Loan Strategy:</strong> If loan features are utilized, monitor interest rates and repayment options to optimize policy performance.</p>
                    </div>
                  </div>
                </div>

                {/* Risk Analysis */}
                <div className="bg-amber-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <AlertTriangle className="w-5 h-5 text-amber-600 mr-2" />
                    Risk Considerations
                  </h3>
                  <div className="space-y-2 text-sm text-gray-700">
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Interest Rate Sensitivity:</strong> Policy performance may vary based on actual vs. illustrated interest rates.</p>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Premium Payment Consistency:</strong> Irregular premium payments may impact long-term policy performance.</p>
                    </div>
                    <div className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p><strong>Market Conditions:</strong> External economic factors may influence actual returns compared to illustrations.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-3 p-4 border-t border-gray-200 flex-shrink-0 bg-gray-50">
              <Button
                variant="secondary"
                onClick={() => setShowAiCommentaryPopup(false)}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
              >
                Close
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  setShowAiCommentaryPopup(false);
                  setActiveTab('analysis-reports');
                }}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
              >
                Generate Full Report
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Disclosure Popup Modal */}
      {showDisclosurePopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <Info className="w-5 h-5 text-blue-600 mr-2" />
                Policy Disclosure Information
              </h2>
              <button
                onClick={() => setShowDisclosurePopup(false)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            {/* Modal Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4">
              {loadingDisclosure ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h4 className="text-xl font-semibold text-gray-700 mb-2">Loading Disclosure Data</h4>
                    <p className="text-gray-600">
                      Please wait while we fetch the policy disclosure information...
                    </p>
                  </div>
                </div>
              ) : disclosureError ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <div className="text-center">
                    <AlertTriangle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-red-800 mb-2">Error Loading Disclosure Data</h4>
                    <p className="text-red-700 mb-4">{disclosureError}</p>
                    <button
                      onClick={async () => {
                        // Use the service method to retry disclosure data fetch
                        await initializeDisclosureData(
                          selectedCustomerData,
                          selectedPolicyData,
                          setLoadingDisclosure,
                          setDisclosureError,
                          setDisclosureData
                        );
                      }}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors font-medium"
                    >
                      Retry
                    </button>
                  </div>
                </div>
              ) : disclosureData.length === 0 ? (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="text-center">
                    <Info className="w-16 h-16 text-yellow-600 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-yellow-800 mb-2">No Disclosure Data Available</h4>
                    <p className="text-yellow-700">
                      No disclosure information is available from the backend for this policy.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-3 py-2 text-center font-bold text-gray-700 w-16 text-sm">
                          S.No
                        </th>
                        <th className="border border-gray-300 px-3 py-2 text-center font-bold text-gray-700 w-48 text-sm">
                          Type
                        </th>
                        <th className="border border-gray-300 px-3 py-2 text-center font-bold text-gray-700 text-sm">
                          Disclosure
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {disclosureData.map((item) => (
                        <tr key={item.DISCLOSURE_ID} className="hover:bg-gray-50 transition-colors">
                          <td className="border border-gray-300 px-3 py-2 text-center font-medium text-gray-900 text-sm">
                            {item.DISCLOSURE_ID}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center font-semibold text-gray-900 text-sm">
                            {item.TYPE}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-justify text-gray-700 leading-relaxed text-sm">
                            {item.DISCLOSURE}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Modal Footer - Always Visible */}
            <div className="flex justify-end p-4 border-t border-gray-200 flex-shrink-0 bg-gray-50">
              <Button
                variant="primary"
                onClick={() => setShowDisclosurePopup(false)}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedScenarios;