import React, { useState, useMemo } from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import AsIsPage from './AsIsPage';
import FaceAmountPage from './FaceAmountPage';
import PremiumPage from './PremiumPage';
import IncomePage from './IncomePage';
import LoanRepaymentPage from './LoanRepaymentPage';
import InterestRatePage from './InterestRatePage';
import Card from '../common/Card';
import Button from '../common/Button';
import {
  ChevronDown,
  ChevronRight,
  Bookmark,
  DollarSign,
  TrendingUp,
  Download,
  Percent,
  BarChart3
} from 'lucide-react';
import SelectedScenariosPage from './SelectedScenariosPage';
import IllustrationTypeDebugger from '../debug/IllustrationTypeDebugger';

interface IllustrationTab {
  id: string;
  typeId: number; // Unique ID from 1 to 6 for backend filtering
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const IllustrationMainPage: React.FC = () => {
  const {
    activeTab,
    setActiveTab,
    selectedCustomerData,
    selectedPolicyData,
    scenarios,
    allowedIllustrationTypes,
    error
  } = useDashboard();

  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const illustrationTabs: IllustrationTab[] = [
    {
      id: 'as-is',
      typeId: 1,
      label: 'As-Is',
      icon: Bookmark,
      description: 'Current policy illustration'
    },
    {
      id: 'face-amount',
      typeId: 2,
      label: 'Face Amount',
      icon: DollarSign,
      description: 'Death benefit amount scenarios'
    },
    {
      id: 'premium',
      typeId: 3,
      label: 'Premium',
      icon: TrendingUp,
      description: 'Premium payment scenarios'
    },
    {
      id: 'interest-rate',
      typeId: 4,
      label: 'Interest Rate',
      icon: Percent,
      description: 'Interest rate based scenarios'
    },
    {
      id: 'income',
      typeId: 5,
      label: 'Full Surrender / Income (Loan & Withdrawal)',
      icon: Download,
      description: 'Loan & withdrawal scenarios'
    },
    {
      id: 'loan-repayment',
      typeId: 6,
      label: 'Loan Repayment',
      icon: Percent,
      description: 'Loan repayment scenarios'
    }
  ];

  // Filter illustration tabs based on backend response
  const filteredIllustrationTabs = useMemo(() => {
    if (!allowedIllustrationTypes || allowedIllustrationTypes.length === 0) {
      return illustrationTabs;
    }
    return illustrationTabs.filter(tab => allowedIllustrationTypes.includes(tab.typeId));
  }, [allowedIllustrationTypes]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(tabId)) {
      newExpanded.delete(tabId);
    } else {
      newExpanded.add(tabId);
    }
    setExpandedSections(newExpanded);
  };

  const getScenariosForCategory = (category: string) => {
    return scenarios?.filter(scenario => scenario.category === category) || [];
  };

  /**
   * Universal date parsing function that works across all browsers
   * Handles multiple date formats and provides extensive debugging
   */
  const parseDate = (dateInput: any): Date | null => {
    if (!dateInput) {
      console.warn('parseDate: No date input provided');
      return null;
    }

    // Convert to string and clean up
    let dateStr = String(dateInput).trim();
    
    if (!dateStr) {
      console.warn('parseDate: Empty date string');
      return null;
    }

    console.log('parseDate: Processing date input:', dateStr);

    try {
      // Remove common suffixes and clean up
      dateStr = dateStr.replace(/\s*(UTC|GMT|EST|PST|CST|MST).*$/i, '').trim();
      
      // Pattern 1: MM-DD-YYYY or MM/DD/YYYY
      const mmDdYyyyRegex = /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/;
      let match = dateStr.match(mmDdYyyyRegex);
      if (match) {
        const [, month, day, year] = match;
        const monthNum = parseInt(month, 10);
        const dayNum = parseInt(day, 10);
        const yearNum = parseInt(year, 10);
        
        if (isValidDateComponents(monthNum, dayNum, yearNum)) {
          const date = createSafeDate(yearNum, monthNum - 1, dayNum);
          if (date) {
            console.log('parseDate: Successfully parsed MM-DD-YYYY format:', date);
            return date;
          }
        }
      }

      // Pattern 2: DD-MM-YYYY or DD/MM/YYYY (European format)
      // Try this if MM-DD-YYYY failed and day > 12
      match = dateStr.match(mmDdYyyyRegex);
      if (match) {
        const [, first, second, year] = match;
        const firstNum = parseInt(first, 10);
        const secondNum = parseInt(second, 10);
        const yearNum = parseInt(year, 10);
        
        // If first number > 12, assume DD-MM-YYYY format
        if (firstNum > 12 && secondNum <= 12 && isValidDateComponents(secondNum, firstNum, yearNum)) {
          const date = createSafeDate(yearNum, secondNum - 1, firstNum);
          if (date) {
            console.log('parseDate: Successfully parsed DD-MM-YYYY format:', date);
            return date;
          }
        }
      }

      // Pattern 3: YYYY-MM-DD or YYYY/MM/DD
      const yyyyMmDdRegex = /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/;
      match = dateStr.match(yyyyMmDdRegex);
      if (match) {
        const [, year, month, day] = match;
        const yearNum = parseInt(year, 10);
        const monthNum = parseInt(month, 10);
        const dayNum = parseInt(day, 10);
        
        if (isValidDateComponents(monthNum, dayNum, yearNum)) {
          const date = createSafeDate(yearNum, monthNum - 1, dayNum);
          if (date) {
            console.log('parseDate: Successfully parsed YYYY-MM-DD format:', date);
            return date;
          }
        }
      }

      // Pattern 4: ISO format (YYYY-MM-DDTHH:mm:ss)
      const isoRegex = /^(\d{4})-(\d{2})-(\d{2})T/;
      match = dateStr.match(isoRegex);
      if (match) {
        const [, year, month, day] = match;
        const yearNum = parseInt(year, 10);
        const monthNum = parseInt(month, 10);
        const dayNum = parseInt(day, 10);
        
        if (isValidDateComponents(monthNum, dayNum, yearNum)) {
          const date = createSafeDate(yearNum, monthNum - 1, dayNum);
          if (date) {
            console.log('parseDate: Successfully parsed ISO format:', date);
            return date;
          }
        }
      }

      // Pattern 5: Timestamp (numbers only)
      if (/^\d+$/.test(dateStr)) {
        const timestamp = parseInt(dateStr, 10);
        let date;
        
        // Check if it's milliseconds (13+ digits) or seconds (10 digits)
        if (dateStr.length >= 13) {
          date = new Date(timestamp);
        } else if (dateStr.length === 10) {
          date = new Date(timestamp * 1000);
        }
        
        if (date && isValidDate(date)) {
          console.log('parseDate: Successfully parsed timestamp:', date);
          return date;
        }
      }

      // Pattern 6: Try browser's native parsing as last resort
      // But only for formats that are likely to work consistently
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        const nativeDate = new Date(dateStr + 'T00:00:00');
        if (isValidDate(nativeDate)) {
          console.log('parseDate: Successfully parsed with native parsing (ISO):', nativeDate);
          return nativeDate;
        }
      }

      console.error('parseDate: Could not parse date with any known pattern:', dateStr);
      return null;

    } catch (error) {
      console.error('parseDate: Exception during date parsing:', dateInput, error);
      return null;
    }
  };

  /**
   * Validates date components
   */
  const isValidDateComponents = (month: number, day: number, year: number): boolean => {
    return (
      Number.isInteger(month) && month >= 1 && month <= 12 &&
      Number.isInteger(day) && day >= 1 && day <= 31 &&
      Number.isInteger(year) && year >= 1900 && year <= 2100
    );
  };

  /**
   * Creates a date safely using individual components
   */
  const createSafeDate = (year: number, month: number, day: number): Date | null => {
    try {
      const date = new Date(year, month, day, 0, 0, 0, 0);
      
      // Verify the date was created correctly
      if (date.getFullYear() === year && 
          date.getMonth() === month && 
          date.getDate() === day) {
        return date;
      }
      
      console.warn('createSafeDate: Date components were adjusted by JavaScript Date constructor');
      return null;
    } catch (error) {
      console.error('createSafeDate: Error creating date:', error);
      return null;
    }
  };

  /**
   * Validates if a date object is valid
   */
  const isValidDate = (date: Date): boolean => {
    return date instanceof Date && 
           !isNaN(date.getTime()) && 
           date.getFullYear() >= 1900 && 
           date.getFullYear() <= 2100;
  };

  /**
   * Formats a number into an ordinal string (e.g., 1 -> 1st, 2 -> 2nd).
   */
  const formatOrdinal = (n: number): string => {
    if (!Number.isInteger(n) || n <= 0) {
      return 'Unknown';
    }

    // Return just the number without ordinal suffix
    return n.toString();
  };

  /**
   * Debug function to log all available data
   */
  const debugPolicyData = () => {
    console.log('=== POLICY DATA DEBUG ===');
    console.log('selectedPolicyData:', selectedPolicyData);
    console.log('selectedCustomerData:', selectedCustomerData);
    
    if (selectedPolicyData) {
      console.log('Available policy data keys:', Object.keys(selectedPolicyData));
      Object.keys(selectedPolicyData).forEach(key => {
        console.log(`${key}:`, selectedPolicyData[key]);
      });
    }
    
    if (selectedCustomerData) {
      console.log('Available customer data keys:', Object.keys(selectedCustomerData));
      Object.keys(selectedCustomerData).forEach(key => {
        console.log(`${key}:`, selectedCustomerData[key]);
      });
    }
    console.log('=== END DEBUG ===');
  };

  /**
   * Calculates the current policy year with comprehensive error handling
   */
  const calculatePolicyYear = useMemo((): string => {
    console.log('calculatePolicyYear: Starting calculation...');
    
    // Debug all available data
    debugPolicyData();
    
    // Try to find issue date from multiple possible fields
    const possibleDateFields = [
      selectedPolicyData?.issueDate,
      selectedPolicyData?.policyStartDate,
      selectedPolicyData?.startDate,
      selectedPolicyData?.effectiveDate,
      selectedPolicyData?.policyDate,
      selectedPolicyData?.inceptionDate,
      selectedPolicyData?.createdDate,
      selectedPolicyData?.dateIssued,
      selectedCustomerData?.policyStartDate,
      selectedCustomerData?.issueDate
    ];

    console.log('calculatePolicyYear: Possible date fields:', possibleDateFields);

    let issueDate = null;
    let dateSource = '';

    // Find the first valid date
    for (let i = 0; i < possibleDateFields.length; i++) {
      if (possibleDateFields[i]) {
        issueDate = possibleDateFields[i];
        dateSource = `Field ${i} (${Object.keys(selectedPolicyData || {})[i] || 'customer data'})`;
        console.log(`calculatePolicyYear: Found date in ${dateSource}:`, issueDate);
        break;
      }
    }

    // If no issue date found, try to use a default or estimate
    if (!issueDate) {
      console.warn('calculatePolicyYear: No issue date found in any field');
      
      // Try to estimate based on customer age and policy type
      const dob = selectedCustomerData?.details?.DOB || selectedCustomerData?.dateOfBirth;
      if (dob) {
        console.log('calculatePolicyYear: Attempting to estimate based on DOB:', dob);
        const birthDate = parseDate(dob);
        if (birthDate) {
          // Assume policy was issued when customer was 25-35 years old (common age for life insurance)
          const estimatedIssueYear = birthDate.getFullYear() + 30; // Assume 30 years old
          const currentYear = new Date().getFullYear();
          const estimatedPolicyYear = currentYear - estimatedIssueYear + 1;
          
          if (estimatedPolicyYear > 0 && estimatedPolicyYear < 100) {
            console.log('calculatePolicyYear: Using estimated policy year:', estimatedPolicyYear);
            return `${formatOrdinal(estimatedPolicyYear)} Policy Year (Estimated)`;
          }
        }
      }
      
      // Final fallback - assume it's a recent policy
      console.log('calculatePolicyYear: Using fallback - assuming 1 year policy');
      return '1 Policy Year (Default)';
    }

    const parsedIssueDate = parseDate(issueDate);
    console.log('calculatePolicyYear: Parsed issue date:', parsedIssueDate, 'from', dateSource);
    
    if (!parsedIssueDate) {
      console.error('calculatePolicyYear: Failed to parse issue date:', issueDate);
      return 'Unknown (Invalid Date)';
    }

    // Get current date and normalize
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    console.log('calculatePolicyYear: Current date:', today);

    // Calculate year difference
    let yearDiff = today.getFullYear() - parsedIssueDate.getFullYear();
    console.log('calculatePolicyYear: Initial year difference:', yearDiff);
    
    // Create anniversary date for this year
    const anniversaryThisYear = new Date(parsedIssueDate);
    anniversaryThisYear.setFullYear(today.getFullYear());
    anniversaryThisYear.setHours(0, 0, 0, 0);
    console.log('calculatePolicyYear: Anniversary this year:', anniversaryThisYear);

    // If today is before this year's anniversary, we're still in the previous policy year
    if (today < anniversaryThisYear) {
      yearDiff--;
      console.log('calculatePolicyYear: Before anniversary, adjusted year diff:', yearDiff);
    }

    const currentPolicyYear = yearDiff + 1;
    console.log('calculatePolicyYear: Final policy year:', currentPolicyYear);

    // Validate the result
    if (!Number.isInteger(currentPolicyYear) || currentPolicyYear < 1) {
      console.error('calculatePolicyYear: Invalid policy year calculated:', currentPolicyYear);
      return 'Unknown (Invalid Calculation)';
    }

    if (currentPolicyYear > 100) {
      console.warn('calculatePolicyYear: Policy year seems too high:', currentPolicyYear);
      return 'Unknown (Year Too High)';
    }

    const result = `${formatOrdinal(currentPolicyYear)}`;
    console.log('calculatePolicyYear: Final result:', result);
    return result;
  }, [selectedPolicyData, selectedCustomerData]);

  // Extract and format premium value
  const formatPremium = (premium: string | undefined): string => {
    if (!premium) return '1,500';

    try {
      const numericValue = premium.replace(/[,$]/g, '').replace(/annually/i, '').trim();
      const match = numericValue.match(/(\d+)/);
      const value = match ? parseInt(match[1]) : 1500;
      return value.toLocaleString();
    } catch (error) {
      console.error('Error formatting premium:', error);
      return '1,500';
    }
  };

  // Extract and format coverage value
  const formatCoverage = (coverage: string | number | undefined): string => {
    if (!coverage) return '250,000';

    try {
      const numericValue = typeof coverage === 'string'
        ? parseInt(coverage.replace(/[,$]/g, ''))
        : coverage;
      return numericValue.toLocaleString();
    } catch (error) {
      console.error('Error formatting coverage:', error);
      return '250,000';
    }
  };

  const renderIllustrationContent = () => {
    try {
      switch (activeTab) {
        case 'selected-scenarios':
          return <SelectedScenariosPage />;
        case 'as-is':
          return <AsIsPage />;
        case 'face-amount':
          return <FaceAmountPage />;
        case 'premium':
          return <PremiumPage />;
        case 'income':
          return <IncomePage />;
        case 'loan-repayment':
          return <LoanRepaymentPage />;
        case 'interest-rate':
          return <InterestRatePage />;
        case 'debug-types':
          return <IllustrationTypeDebugger />;
        default:
          return <AsIsPage />;
      }
    } catch (error) {
      console.error('Error rendering illustration content:', error);
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-semibold">Error Loading Content</h3>
          <p className="text-red-700 text-sm mt-1">
            There was an error loading this illustration page. Please try again.
          </p>
        </div>
      );
    }
  };

  return (
    <div className="space-y-6 px-6">
      {/* Current Policy Information Card - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <Card className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Current Policy Information</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 font-medium">Policy Number</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.policyNumber || selectedPolicyData?.policyNumber || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Customer Name</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.name || selectedCustomerData?.customerName || selectedPolicyData?.customerName || 'John Michael Smith'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Customer ID</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.customerId || selectedCustomerData?.id || selectedPolicyData?.customerId || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Date of Birth</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedCustomerData?.details?.DOB || selectedCustomerData?.dateOfBirth || selectedCustomerData?.dob || '03-15-1985'}
                </p>
              </div>
            </div>

            {/* Second Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 font-medium">Policy Type</p>
                <p className="text-base font-bold text-gray-900">
                  {selectedPolicyData?.name || selectedPolicyData?.policyType || selectedPolicyData?.type || 'Whole Life Insurance'}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 font-medium">Annual Premium</p>
                  <p className="text-base font-bold text-gray-900">
                    $ {formatPremium(selectedPolicyData?.premium || selectedPolicyData?.annualPremium)}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500 font-medium">Face Amount</p>
                <p className="text-base font-bold text-gray-900">
                  $ {formatCoverage(selectedPolicyData?.coverage || selectedPolicyData?.faceAmount || selectedPolicyData?.deathBenefit)}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 font-medium">Current Policy Year</p>
                  <p className="text-base font-bold text-gray-900">
                    {calculatePolicyYear}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Policy Selection Required Message */}
      {!selectedPolicyData && (
        <div className="px-4 pt-4">
          <Card className="bg-yellow-50 border-yellow-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 text-lg font-bold">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800">Policy Selection Required</h3>
                <p className="text-yellow-700 mb-3">
                  Please select a policy first to view your selected scenarios and illustrations.
                </p>
                <Button
                  variant="primary"
                  onClick={() => setActiveTab('policy-selection')}
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      <div className="flex-1 px-4 pt-4">
        {/* Error Display */}
        {error && error.includes('illustration types') && (
          <div className="mb-4">
            <Card className="bg-yellow-50 border-yellow-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 text-lg font-bold">⚠</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-yellow-800">Illustration Types Loading Issue</h3>
                  <p className="text-yellow-700">
                    {error} - Showing all illustration types as fallback.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Illustration Types Filter Info */}
        {filteredIllustrationTabs.length < illustrationTabs.length && !error && (
          <div className="mb-4">
            <Card className="bg-blue-50 border-blue-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-lg font-bold">ℹ</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-blue-800">Filtered Illustration Types</h3>
                  <p className="text-blue-700">
                    Showing {filteredIllustrationTabs.length} of {illustrationTabs.length} illustration types based on your policy configuration.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        <div className="space-y-4">
          {filteredIllustrationTabs.map((tab) => {
            const Icon = tab.icon;
            const isExpanded = expandedSections.has(tab.id);
            const isActive = activeTab === tab.id;
            const tabScenarios = getScenariosForCategory(tab.id);

            return (
              <div key={tab.id} className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Dropdown Header */}
                <button
                  onClick={() => handleTabClick(tab.id)}
                  className={`w-full flex items-center justify-between p-6 text-left transition-all duration-200 hover:bg-gray-50 rounded-lg ${
                    isExpanded ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-lg ${
                      isActive
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{tab.label}</h3>
                      <p className="text-sm text-gray-600">{tab.description}</p>
                      {tabScenarios.length > 0 && tab.id !== 'face-amount' && tab.id !== 'loan-repayment' && (
                        <p className="text-xs text-blue-600 mt-1">
                          {tabScenarios.length} scenario{tabScenarios.length !== 1 ? 's' : ''} available
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isExpanded ? (
                      <ChevronDown className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                    )}
                  </div>
                </button>

                {/* Dropdown Content */}
                {isExpanded && (
                  <div className="border-t border-gray-200 bg-gray-50">
                    <div className="p-6">
                      {/* Scenarios List - Hide for Face Amount and Loan Repayment tabs since they use database storage */}
                      {tabScenarios.length > 0 && tab.id !== 'face-amount' && tab.id !== 'loan-repayment' && (
                        <div className="mb-6">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Available Scenarios</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {tabScenarios.map((scenario) => (
                              <div
                                key={scenario.id}
                                className="bg-white rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-200 cursor-pointer"
                              >
                                <h5 className="font-medium text-gray-900 text-sm">{scenario.name}</h5>
                                <p className="text-xs text-gray-600 mt-1">{scenario.asIsDetails}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Show Content Directly Under Tab When Active */}
                      {isActive && (
                        <div>
                          {renderIllustrationContent()}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default IllustrationMainPage;